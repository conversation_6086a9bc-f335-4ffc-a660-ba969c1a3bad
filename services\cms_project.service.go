package services

import (
	"net/http"
	"sync"
	"time"

	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
	"gitlab.finema.co/finema/idin-core/utils"
)

type ICMSProjectService interface {
	Create(input *CMSProjectCreatePayload) (*models.CMSProject, core.IError)
	Update(id string, input *CMSProjectUpdatePayload) (*models.CMSProject, core.IError)
	Find(id string) (*models.CMSProject, core.IError)
	Pagination(filters *CMSProjectFilters, pageOptions *core.PageOptions) (*repository.Pagination[models.CMSProject], core.IError)
	Delete(id string) core.IError
	UpdateStatus(id string, status models.CMSProjectStatus) (*models.CMSProject, core.IError)
	GetDashboardData() (*CMSDashboardResponse, core.IError)
}

type cmsProjectService struct {
	ctx core.IContext
}

func (s cmsProjectService) Create(input *CMSProjectCreatePayload) (*models.CMSProject, core.IError) {
	userID := s.ctx.GetUser().ID

	basePhaseModel := models.NewBaseModelHardDelete()

	// endDate, _ := time.Parse(time.DateOnly, utils.ToNonPointer(input.EndDate))
	cmsProject := &models.CMSProject{
		BaseModel:    models.NewBaseModel(),
		PhaseID:      &basePhaseModel.ID,
		CreatedByID:  &userID,
		UpdatedByID:  &userID,
		MinistryID:   input.MinistryID,
		DepartmentID: input.DepartmentID,
		DivisionID:   input.DivisionID,
		Name:         input.Name,
		Type:         models.CMSProjectType(input.Type),
		Domain:       input.Domain,
		ContactName:  input.ContactName,
		ContactPhone: input.ContactPhone,
		ContactEmail: input.ContactEmail,
		Remark:       input.Remark,
		Status:       models.CMSProjectStatus(input.Status),
	}

	ierr := repo.CMSProject(s.ctx).Create(cmsProject)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	if input.Phase != nil {
		startDate, _ := time.Parse(time.DateOnly, input.Phase.StartDate)
		endDate, _ := time.Parse(time.DateOnly, input.Phase.EndDate)
		ierr := repo.CMSProjectPhase(s.ctx).Create(&models.CMSProjectPhase{
			BaseModelHardDelete: basePhaseModel,
			CMSProjectID:        cmsProject.ID,
			Phase:               input.Phase.Phase,
			WorkPhase:           input.Phase.WorkPhase,
			StartDate:           &startDate,
			EndDate:             &endDate,
			Type:                models.CMSProjectType(input.Type),
			FileURL:             input.Phase.FileURL,
		})
		if ierr != nil {
			return nil, s.ctx.NewError(ierr, ierr)
		}
	}

	return s.Find(cmsProject.ID)
}

func (s cmsProjectService) Update(id string, input *CMSProjectUpdatePayload) (*models.CMSProject, core.IError) {
	userID := s.ctx.GetUser().ID

	cmsProject, ierr := repo.CMSProject(s.ctx).FindOne("id = ?", id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	// Update only provided fields
	if input.MinistryID != "" {
		cmsProject.MinistryID = input.MinistryID
	}
	if input.DepartmentID != "" {
		cmsProject.DepartmentID = input.DepartmentID
	}
	if input.DivisionID != "" {
		cmsProject.DivisionID = input.DivisionID
	}
	if input.Name != "" {
		cmsProject.Name = input.Name
	}
	if input.Type != "" {
		cmsProject.Type = models.CMSProjectType(input.Type)
	}
	if input.Domain != "" {
		cmsProject.Domain = input.Domain
	}
	if input.ContactName != "" {
		cmsProject.ContactName = input.ContactName
	}
	if input.ContactPhone != "" {
		cmsProject.ContactPhone = input.ContactPhone
	}
	if input.ContactEmail != "" {
		cmsProject.ContactEmail = input.ContactEmail
	}
	if input.Remark != "" {
		cmsProject.Remark = input.Remark
	}
	if input.Status != "" {
		cmsProject.Status = models.CMSProjectStatus(input.Status)
	}
	cmsProject.UpdatedByID = &userID

	ierr = repo.CMSProject(s.ctx).Where("id = ?", id).Updates(cmsProject)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(cmsProject.ID)
}

func (s cmsProjectService) Find(id string) (*models.CMSProject, core.IError) {
	return repo.CMSProject(s.ctx, repo.CMSProjectWithRelations()).FindOne("id = ?", id)
}

func (s cmsProjectService) Pagination(filters *CMSProjectFilters, pageOptions *core.PageOptions) (*repository.Pagination[models.CMSProject], core.IError) {
	return repo.CMSProject(
		s.ctx,
		repo.CMSProjectOrderBy(pageOptions),
		repo.CMSProjectByStatus(filters.Status),
		repo.CMSProjectByType(filters.Type),
		repo.CMSProjectByMinistry(filters.MinistryID),
		repo.CMSProjectByDepartment(filters.DepartmentID),
		repo.CMSProjectByDivision(filters.DivisionID),
		repo.CMSProjectByPhase(filters.Phase, filters.WorkPhase, filters.StartDate, filters.EndDate, filters.IsNearlyExpired),
		repo.CMSProjectBySearch(pageOptions.Q),
		repo.CMSProjectWithRelations()).
		Pagination(pageOptions)
}

func (s cmsProjectService) Delete(id string) core.IError {
	userID := s.ctx.GetUser().ID

	cmsProject, ierr := repo.CMSProject(s.ctx).FindOne("id = ?", id)
	if ierr != nil {
		return s.ctx.NewError(ierr, ierr)
	}

	cmsProject.DeletedByID = &userID
	cmsProject.UpdatedAt = utils.GetCurrentDateTime()

	return repo.CMSProject(s.ctx).Delete(cmsProject)
}

func (s cmsProjectService) UpdateStatus(id string, status models.CMSProjectStatus) (*models.CMSProject, core.IError) {
	userID := s.ctx.GetUser().ID

	cmsProject, ierr := repo.CMSProject(s.ctx).FindOne("id = ?", id)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	cmsProject.Status = status
	cmsProject.UpdatedByID = &userID

	ierr = repo.CMSProject(s.ctx).Updates(cmsProject)
	if ierr != nil {
		return nil, s.ctx.NewError(ierr, ierr)
	}

	return s.Find(cmsProject.ID)
}

func (s cmsProjectService) GetDashboardData() (*CMSDashboardResponse, core.IError) {
	// Use channels to collect results from parallel operations
	type projectStatsResult struct {
		data *CMSProjectStats
		err  core.IError
	}

	type latestProjectsResult struct {
		data []models.CMSProject
		err  core.IError
	}

	type nearlyExpiredResult struct {
		data []models.CMSProject
		err  core.IError
	}

	projectStatsCh := make(chan projectStatsResult, 1)
	latestProjectsCh := make(chan latestProjectsResult, 1)
	nearlyExpiredCh := make(chan nearlyExpiredResult, 1)

	var wg sync.WaitGroup
	wg.Add(3)

	// Get project statistics by status
	go func() {
		defer wg.Done()
		data, err := s.getCMSProjectStats()
		projectStatsCh <- projectStatsResult{data: data, err: err}
	}()

	// Get 5 latest projects
	go func() {
		defer wg.Done()
		data, err := s.getLatestCMSProjects()
		latestProjectsCh <- latestProjectsResult{data: data, err: err}
	}()

	// Get nearly expired projects (3 months)
	go func() {
		defer wg.Done()
		data, err := s.getNearlyExpiredCMSProjects()
		nearlyExpiredCh <- nearlyExpiredResult{data: data, err: err}
	}()

	// Wait for all goroutines to complete
	wg.Wait()

	// Collect results from channels
	projectStatsRes := <-projectStatsCh
	if projectStatsRes.err != nil {
		return nil, projectStatsRes.err
	}

	latestProjectsRes := <-latestProjectsCh
	if latestProjectsRes.err != nil {
		return nil, latestProjectsRes.err
	}

	nearlyExpiredRes := <-nearlyExpiredCh
	if nearlyExpiredRes.err != nil {
		return nil, nearlyExpiredRes.err
	}

	return &CMSDashboardResponse{
		ProjectStats:          projectStatsRes.data,
		LatestProjects:        latestProjectsRes.data,
		NearlyExpiredProjects: nearlyExpiredRes.data,
	}, nil
}

func (s cmsProjectService) getCMSProjectStats() (*CMSProjectStats, core.IError) {
	var stats CMSProjectStats

	// Get total count
	count, err := repo.CMSProject(s.ctx).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get total CMS project count",
		}, err)
	}
	stats.Total = count

	// Get count by status
	draftCount, err := repo.CMSProject(s.ctx).Where("status = ?", models.CMSProjectStatusDraft).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get draft CMS project count",
		}, err)
	}
	stats.Draft = draftCount

	newCount, err := repo.CMSProject(s.ctx).Where("status = ?", models.CMSProjectStatusNew).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get new CMS project count",
		}, err)
	}
	stats.New = newCount

	inProcessCount, err := repo.CMSProject(s.ctx).Where("status = ?", models.CMSProjectStatusInProcess).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get in-process CMS project count",
		}, err)
	}
	stats.InProcess = inProcessCount

	activeCount, err := repo.CMSProject(s.ctx).Where("status = ?", models.CMSProjectStatusActive).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get active CMS project count",
		}, err)
	}
	stats.Active = activeCount

	closeCount, err := repo.CMSProject(s.ctx).Where("status = ?", models.CMSProjectStatusClose).Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get closed CMS project count",
		}, err)
	}
	stats.Close = closeCount

	// Get nearly expired count (projects with phases ending within 3 months)
	now := time.Now()
	threeMonthsFromNow := now.AddDate(0, 3, 0) // Add 3 months to current date

	nearlyExpiredCount, err := repo.CMSProject(s.ctx).
		Where("cms_projects.id IN (SELECT DISTINCT cms_project_id FROM cms_project_phases WHERE end_date BETWEEN ? AND ?)",
			now, threeMonthsFromNow).
		Count()
	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "COUNT_FAILED",
			Message: "Failed to get nearly expired CMS project count",
		}, err)
	}
	stats.NearlyExpired = nearlyExpiredCount

	return &stats, nil
}

func (s cmsProjectService) getLatestCMSProjects() ([]models.CMSProject, core.IError) {
	var projects []models.CMSProject

	projects, err := repo.CMSProject(s.ctx, repo.CMSProjectWithRelations()).
		Order("created_at DESC").
		Limit(10).
		FindAll()

	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FIND_FAILED",
			Message: "Failed to get latest CMS projects",
		}, err)
	}

	return projects, nil
}

// getNearlyExpiredCMSProjects base on current phase end_date
func (s cmsProjectService) getNearlyExpiredCMSProjects() ([]models.CMSProject, core.IError) {
	now := time.Now()
	threeMonthsFromNow := now.AddDate(0, 3, 0) // Add 3 months to current date

	// Find projects that have current phases (active phases) that will expire within 3 months
	// Use a subquery to find projects with current phases ending soon, then get the full project data
	var projects []models.CMSProject

	// Use a subquery to find projects with current phases expiring within 3 months
	projects, err := repo.CMSProject(s.ctx, repo.CMSProjectWithRelations()).
		Where("cms_projects.id IN (SELECT DISTINCT cms_project_id FROM cms_project_phases WHERE end_date BETWEEN ? AND ?)",
							now, threeMonthsFromNow).
		Order("cms_projects.created_at DESC"). // Order by project creation date
		FindAll()

	if err != nil {
		return nil, s.ctx.NewError(core.Error{
			Status:  http.StatusInternalServerError,
			Code:    "FIND_FAILED",
			Message: "Failed to get nearly expired CMS projects",
		}, err)
	}

	return projects, nil
}

func NewCMSProjectService(ctx core.IContext) ICMSProjectService {
	return &cmsProjectService{ctx: ctx}
}
